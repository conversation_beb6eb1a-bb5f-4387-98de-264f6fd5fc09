(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1745],{18959:(e,s,t)=>{Promise.resolve().then(t.bind(t,47539))},47539:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>_});var a=t(95155),l=t(12115),r=t(35695),n=t(83298);let i=l.forwardRef(function(e,s){let{title:t,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?l.createElement("title",{id:a},t):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))});var c=t(27572),d=t(5500),o=t(30192),x=t(82771),m=t(58397),h=t(28960),u=t(92975),g=t(55628),p=t(10184),j=t(65529),v=t(78046);let y=l.forwardRef(function(e,s){let{title:t,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?l.createElement("title",{id:a},t):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))}),b=l.forwardRef(function(e,s){let{title:t,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?l.createElement("title",{id:a},t):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}),N=e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),f=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:4}).format(e),w=(e,s)=>{if(0===s)return{percentage:0,isPositive:e>0};let t=(e-s)/s*100;return{percentage:Math.abs(t),isPositive:t>=0}},k=e=>{let{title:s,value:t,trend:l,icon:r,subtitle:n}=e;return(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:s}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:"number"==typeof t?N(t):t}),l&&(0,a.jsxs)("span",{className:"text-sm px-2 py-1 rounded-md flex items-center space-x-1 ".concat(l.isPositive?"text-green-400 bg-green-400/10":"text-red-400 bg-red-400/10"),children:[l.isPositive?(0,a.jsx)(c.A,{className:"w-3 h-3"}):(0,a.jsx)(i,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[l.percentage.toFixed(1),"%"]})]})]}),n&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:n})]}),(0,a.jsx)("div",{className:"text-gray-500",children:r})]})})};function A(){var e;(0,r.useRouter)();let s=(0,n.R)(),[t,A]=(0,l.useState)(null),[_,M]=(0,l.useState)(null),[C,S]=(0,l.useState)([]),[E,q]=(0,l.useState)([]),[O,P]=(0,l.useState)(!0),[D,L]=(0,l.useState)(null),[F,I]=(0,l.useState)("30"),[U,B]=(0,l.useState)(""),[R,G]=(0,l.useState)("overview");(0,l.useEffect)(()=>{T()},[]),(0,l.useEffect)(()=>{var e;(null==s?void 0:s.loading)!==!0&&((null==s||null==(e=s.user)?void 0:e.id)?(L(null),W()):(null==s?void 0:s.loading)===!1&&(L("Authentication required. Please log in to view analytics."),P(!1)))},[null==s||null==(e=s.user)?void 0:e.id,null==s?void 0:s.loading,F,U]);let T=async()=>{try{let e=await fetch("/api/custom-configs");if(e.ok){let s=await e.json();q(s)}}catch(e){}},W=(0,l.useCallback)(async()=>{try{var e;P(!0),L(null);try{let e=await fetch("/api/analytics/summary?groupBy=provider");if(401===e.status)throw Error("Authentication required. Please log in to view analytics.")}catch(e){}let s=new URLSearchParams,t=new Date;t.setDate(t.getDate()-parseInt(F)),s.append("startDate",t.toISOString()),U&&s.append("customApiConfigId",U);let a=await fetch("/api/analytics/summary?".concat(s.toString(),"&groupBy=provider"));if(!a.ok){let e=await a.text();if(401===a.status)throw Error("Authentication required. Please log in to view analytics.");throw Error("Failed to fetch analytics data: ".concat(a.status," ").concat(e))}let l=await a.json();if(A(l),(null==(e=l.summary)?void 0:e.total_requests)>0)try{let e=new URLSearchParams,t=new Date;t.setDate(t.getDate()-2*parseInt(F));let a=new Date;a.setDate(a.getDate()-parseInt(F)),e.append("startDate",t.toISOString()),e.append("endDate",a.toISOString()),U&&e.append("customApiConfigId",U);let[l,r]=await Promise.all([fetch("/api/analytics/summary?".concat(e.toString(),"&groupBy=provider")),fetch("/api/analytics/summary?".concat(s.toString(),"&groupBy=day"))]),n=l.ok?await l.json():null,i=r.ok?await r.json():null;if(M(n),null==i?void 0:i.grouped_data){let e=i.grouped_data.map(e=>({date:e.period||e.name,cost:e.cost||0,requests:e.requests||0,tokens:(e.input_tokens||0)+(e.output_tokens||0),latency:e.avg_latency||0}));S(e)}}catch(e){}}catch(e){L(e.message)}finally{P(!1)}},[F,U]);if((null==s?void 0:s.loading)!==!1||O)return(0,a.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},s))})]}),(0,a.jsxs)("div",{className:"fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:(null==s?void 0:s.loading)!==!1?"Authenticating...":"Loading analytics..."})]})]})});if(D)return(0,a.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,a.jsx)("div",{className:"w-full px-6 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Analytics"}),(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto",children:[(0,a.jsxs)("p",{className:"text-red-400 mb-4",children:["Error loading analytics: ",D]}),(0,a.jsx)("button",{onClick:W,className:"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors",children:"Retry"})]})]})})});let Z=null==t?void 0:t.summary,H=null==_?void 0:_.summary,Q=H?w((null==Z?void 0:Z.total_cost)||0,H.total_cost):null,K=H?w((null==Z?void 0:Z.total_requests)||0,H.total_requests):null,Y=H?w((null==Z?void 0:Z.average_latency)||0,H.average_latency||0):null,z=H?w((null==Z?void 0:Z.success_rate)||0,H.success_rate):null;return(0,a.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{className:"px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors",children:"Workspace"}),(0,a.jsx)("button",{className:"px-3 py-1 text-sm bg-cyan-500 text-white rounded",children:"Organisation"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search Filter",className:"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32"})]}),(0,a.jsxs)("select",{value:F,onChange:e=>I(e.target.value),className:"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500",children:[(0,a.jsx)("option",{value:"7",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90",children:"Last 90 days"})]})]})]})})}),(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>G("overview"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("overview"===R?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Overview"})]}),(0,a.jsxs)("button",{onClick:()=>G("users"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("users"===R?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,a.jsx)(b,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Users"})]}),(0,a.jsxs)("button",{onClick:()=>G("errors"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("errors"===R?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Errors"})]}),(0,a.jsxs)("button",{onClick:()=>G("cache"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("cache"===R?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,a.jsx)(y,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Cache"})]}),(0,a.jsxs)("button",{onClick:()=>G("feedback"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("feedback"===R?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Feedback"})]}),(0,a.jsxs)("button",{onClick:()=>G("metadata"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("metadata"===R?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Metadata"})]})]})})}),(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:["overview"===R&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(k,{title:"Total Request Made",value:(null==Z?void 0:Z.total_requests)||0,trend:K,icon:(0,a.jsx)(d.A,{className:"w-6 h-6"})}),(0,a.jsx)(k,{title:"Average Latency",value:"".concat(Math.round((null==Z?void 0:Z.average_latency)||0),"ms"),trend:Y,icon:(0,a.jsx)(x.A,{className:"w-6 h-6"})}),(0,a.jsx)(k,{title:"User Feedback",value:"".concat(((null==Z?void 0:Z.success_rate)||0).toFixed(1),"%"),trend:z,icon:(0,a.jsx)(o.A,{className:"w-6 h-6"})}),(0,a.jsx)(k,{title:"Total Cost",value:f((null==Z?void 0:Z.total_cost)||0),trend:Q,icon:(0,a.jsx)(h.A,{className:"w-6 h-6"})})]}),(!Z||0===Z.total_requests)&&!O&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Analytics Data Yet"}),(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:"Start making API requests to see your analytics data here."}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"Analytics will appear once you begin using your API configurations."}),!1]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cost"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:f((null==Z?void 0:Z.total_cost)||0)}),Q&&(0,a.jsxs)("span",{className:"text-sm px-2 py-1 rounded flex items-center space-x-1 ".concat(Q.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"),children:[Q.isPositive?(0,a.jsx)(c.A,{className:"w-3 h-3"}):(0,a.jsx)(i,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[Q.percentage.toFixed(1),"%"]})]})]})]}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(h.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:C.length>0?(0,a.jsx)("div",{className:"absolute inset-4",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"costGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#10b981",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#10b981",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("line",{x1:"0",y1:24*s,x2:"400",y2:24*s,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},s)),(0,a.jsx)("polyline",{fill:"none",stroke:"#10b981",strokeWidth:"2",points:C.map((e,s)=>{let t=s/Math.max(C.length-1,1)*400,a=Math.max(...C.map(e=>e.cost)),l=120-e.cost/a*100;return"".concat(t,",").concat(l)}).join(" ")}),(0,a.jsx)("polygon",{fill:"url(#costGradient)",points:"0,120 ".concat(C.map((e,s)=>{let t=s/Math.max(C.length-1,1)*400,a=Math.max(...C.map(e=>e.cost)),l=120-e.cost/a*100;return"".concat(t,",").concat(l)}).join(" ")," 400,120")})]})}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Latency"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-white",children:[Math.round((null==Z?void 0:Z.average_latency)||0),"ms"]}),Y&&(0,a.jsxs)("span",{className:"text-sm px-2 py-1 rounded flex items-center space-x-1 ".concat(Y.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"),children:[Y.isPositive?(0,a.jsx)(c.A,{className:"w-3 h-3"}):(0,a.jsx)(i,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[Y.percentage.toFixed(1),"%"]})]})]})]}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(x.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:C.length>0?(0,a.jsx)("div",{className:"absolute inset-4",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"latencyGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#f59e0b",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#f59e0b",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("line",{x1:"0",y1:24*s,x2:"400",y2:24*s,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},s)),(0,a.jsx)("polyline",{fill:"none",stroke:"#f59e0b",strokeWidth:"2",points:C.map((e,s)=>{let t=s/Math.max(C.length-1,1)*400,a=Math.max(...C.map(e=>e.latency||0)),l=120-(e.latency||0)/Math.max(a,1)*100;return"".concat(t,",").concat(l)}).join(" ")}),(0,a.jsx)("polygon",{fill:"url(#latencyGradient)",points:"0,120 ".concat(C.map((e,s)=>{let t=s/Math.max(C.length-1,1)*400,a=Math.max(...C.map(e=>e.latency||0)),l=120-(e.latency||0)/Math.max(a,1)*100;return"".concat(t,",").concat(l)}).join(" ")," 400,120")})]})}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Tokens Used"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"March 28"})]}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(m.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:N((null==Z?void 0:Z.total_tokens)||0)}),(0,a.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,a.jsx)(c.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"8.39%"})]})]}),(0,a.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,a.jsxs)("div",{className:"absolute inset-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Input Token"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Output Token"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Total Token"})]})]}),(0,a.jsx)("div",{className:"relative h-16",children:[...Array(20)].map((e,s)=>(0,a.jsx)("div",{className:"absolute w-1 h-1 rounded-full ".concat(s%3==0?"bg-yellow-500":s%3==1?"bg-green-500":"bg-blue-500"),style:{left:"".concat(90*Math.random(),"%"),top:"".concat(80*Math.random(),"%")}},s))})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Requests"})}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(j.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:N((null==Z?void 0:Z.total_requests)||0)}),(0,a.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,a.jsx)(c.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"3.39%"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:null==t?void 0:t.grouped_data.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-20 text-sm text-gray-400 truncate",children:e.name}),(0,a.jsx)("div",{className:"flex-1 mx-3",children:(0,a.jsx)("div",{className:"h-2 bg-gray-800 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full rounded-full ".concat(0===s?"bg-pink-500":1===s?"bg-purple-500":2===s?"bg-cyan-500":3===s?"bg-green-500":"bg-yellow-500"),style:{width:"".concat(e.requests/((null==Z?void 0:Z.total_requests)||1)*100,"%")}})})}),(0,a.jsx)("div",{className:"text-sm text-gray-400 w-12 text-right",children:N(e.requests)})]},e.name))})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unique Users"})}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(b,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:(null==Z?void 0:Z.successful_requests)||0}),(0,a.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,a.jsx)(c.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"3.39%"})]})]}),(0,a.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,a.jsx)("div",{className:"absolute inset-4",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 200 80",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"waveGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#8b5cf6",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#8b5cf6",stopOpacity:"0"})]})}),(0,a.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40",fill:"none",stroke:"#8b5cf6",strokeWidth:"2"}),(0,a.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z",fill:"url(#waveGradient)"})]})})})]})]})]}),"users"===R&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(b,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Users Analytics"}),(0,a.jsx)("p",{className:"text-gray-400",children:"User analytics coming soon..."})]}),"errors"===R&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Error Analytics"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Error analytics coming soon..."})]}),"cache"===R&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(y,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Cache Analytics"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Cache analytics coming soon..."})]}),"feedback"===R&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Feedback Analytics"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Feedback analytics coming soon..."})]}),"metadata"===R&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Metadata Analytics"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Metadata analytics coming soon..."})]})]})]})}function _(){return(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},s))})]})})}),children:(0,a.jsx)(A,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,9299,9420,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(18959)),_N_E=e.O()}]);