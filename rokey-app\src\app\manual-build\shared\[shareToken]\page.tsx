'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ReactFlow, Background, Controls, MiniMap, useNodesState, useEdgesState } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { WorkflowNode, WorkflowEdge, ManualBuildWorkflow } from '@/types/manualBuild';
import { nodeTypes } from '@/components/manual-build/nodes';
import { 
  ShareIcon, 
  EyeIcon, 
  PencilIcon, 
  DocumentDuplicateIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface SharedWorkflowPageProps {
  params: Promise<{ shareToken: string }>;
}

interface SharePermissions {
  canView: boolean;
  canEdit: boolean;
  canShare: boolean;
  canDelete: boolean;
  canExport: boolean;
}

export default function SharedWorkflowPage({ params }: SharedWorkflowPageProps) {
  const resolvedParams = useParams();
  const router = useRouter();
  const shareToken = resolvedParams?.shareToken as string;
  
  const [workflow, setWorkflow] = useState<ManualBuildWorkflow | null>(null);
  const [permissions, setPermissions] = useState<SharePermissions | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shareInfo, setShareInfo] = useState<any>(null);

  useEffect(() => {
    if (shareToken) {
      loadSharedWorkflow();
    }
  }, [shareToken]);

  const loadSharedWorkflow = async () => {
    try {
      const response = await fetch(`/api/manual-build/shared/${shareToken}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load shared workflow');
      }

      const data = await response.json();
      setWorkflow(data.workflow);
      setPermissions(data.permissions);
      setShareInfo(data.share);
      
      // Set nodes and edges for React Flow
      setNodes(data.workflow.nodes || []);
      setEdges(data.workflow.edges || []);

    } catch (error) {
      console.error('Failed to load shared workflow:', error);
      setError(error instanceof Error ? error.message : 'Failed to load workflow');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyWorkflow = async () => {
    if (!workflow || !permissions?.canExport) return;

    try {
      const response = await fetch(`/api/manual-build/shared/${shareToken}/copy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `${workflow.name} (Copy)`
        }),
      });

      if (response.ok) {
        const data = await response.json();
        router.push(`/manual-build/${data.workflow.id}`);
      }
    } catch (error) {
      console.error('Failed to copy workflow:', error);
    }
  };

  const handleExportWorkflow = async () => {
    if (!workflow || !permissions?.canExport) return;

    try {
      const response = await fetch(`/api/manual-build/shared/${shareToken}/export`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${workflow.name}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export workflow:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Loading shared workflow...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-[#040716] flex items-center justify-center">
        <div className="max-w-md w-full text-center">
          <ExclamationTriangleIcon className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-gray-400 mb-6">{error}</p>
          <button
            onClick={() => router.push('/manual-build')}
            className="px-6 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors"
          >
            Go to Manual Build
          </button>
        </div>
      </div>
    );
  }

  if (!workflow || !permissions) {
    return (
      <div className="h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Workflow not found</div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-[#040716] flex flex-col">
      {/* Header */}
      <div className="bg-gray-900/50 border-b border-gray-800 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <ShareIcon className="w-6 h-6 text-[#ff6b35]" />
            <div>
              <h1 className="text-xl font-bold text-white">{workflow.name}</h1>
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <span>Shared workflow</span>
                {shareInfo?.is_public && (
                  <span className="flex items-center gap-1">
                    <EyeIcon className="w-4 h-4" />
                    Public
                  </span>
                )}
                {shareInfo?.expires_at && (
                  <span>
                    Expires {new Date(shareInfo.expires_at).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {permissions.canExport && (
              <>
                <button
                  onClick={handleExportWorkflow}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <ArrowDownTrayIcon className="w-4 h-4" />
                  Export
                </button>
                
                <button
                  onClick={handleCopyWorkflow}
                  className="flex items-center gap-2 px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors"
                >
                  <DocumentDuplicateIcon className="w-4 h-4" />
                  Copy to My Workflows
                </button>
              </>
            )}

            {permissions.canEdit && (
              <button
                onClick={() => router.push(`/manual-build/${workflow.id}`)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors"
              >
                <PencilIcon className="w-4 h-4" />
                Edit
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Workflow Viewer */}
      <div className="flex-1 relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={permissions.canEdit ? onNodesChange : undefined}
          onEdgesChange={permissions.canEdit ? onEdgesChange : undefined}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
          className="bg-[#040716]"
          nodesDraggable={permissions.canEdit}
          nodesConnectable={permissions.canEdit}
          elementsSelectable={permissions.canEdit}
        >
          <Background
            color="#1f2937"
            gap={20}
            size={1}
            variant="dots"
          />
          <Controls
            className="bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm"
            showInteractive={false}
          />
          <MiniMap
            className="bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm"
            nodeColor="#ff6b35"
            maskColor="rgba(0, 0, 0, 0.2)"
          />
        </ReactFlow>

        {/* Read-only overlay */}
        {!permissions.canEdit && (
          <div className="absolute top-4 left-4 bg-yellow-900/20 border border-yellow-700/50 rounded-lg px-4 py-2">
            <div className="flex items-center gap-2 text-yellow-300">
              <EyeIcon className="w-4 h-4" />
              <span className="text-sm">Read-only view</span>
            </div>
          </div>
        )}
      </div>

      {/* Workflow Info */}
      {workflow.description && (
        <div className="bg-gray-900/50 border-t border-gray-800 px-6 py-4">
          <p className="text-gray-300 text-sm">{workflow.description}</p>
        </div>
      )}
    </div>
  );
}
